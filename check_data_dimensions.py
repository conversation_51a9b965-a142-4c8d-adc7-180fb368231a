#!/usr/bin/env python3
"""
检查数据文件的维度
"""

import numpy as np
import os

def check_data_file(file_path):
    """检查数据文件的维度"""
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    try:
        data = np.load(file_path)
        print(f"✅ 文件: {file_path}")
        print(f"   形状: {data.shape}")
        print(f"   数据类型: {data.dtype}")
        
        if data.dtype in [np.complex64, np.complex128]:
            print("   数据类型: 复数")
            # 如果是复数，转换为实数表示的维度
            real_shape = (*data.shape, 2)
            print(f"   转换为实数表示后的形状: {real_shape}")
        else:
            print("   数据类型: 实数")
        
        # 计算展平后的维度
        if len(data.shape) >= 3:
            if data.dtype in [np.complex64, np.complex128]:
                flattened_dim = data.shape[1] * data.shape[2] * 2
            else:
                flattened_dim = data.shape[1] * data.shape[2] * data.shape[3] if len(data.shape) == 4 else data.shape[1] * data.shape[2]
            print(f"   单个样本展平后维度: {flattened_dim}")
        
        return data.shape
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return None

def main():
    print("🔍 检查数据文件维度")
    print("=" * 50)
    
    # 检查训练数据
    data_path = "D:/python/challenge/phase1/CompetitionData1/Round1TrainData1.npy"
    label_path = "D:/python/challenge/phase1/CompetitionData1/Round1TrainLabel2.npy"
    
    print("📊 训练数据:")
    data_shape = check_data_file(data_path)
    
    print("\n📊 标签数据:")
    label_shape = check_data_file(label_path)
    
    # 推荐模型参数
    if data_shape and label_shape:
        print("\n🎯 推荐的模型参数:")
        if len(data_shape) >= 3:
            if len(data_shape) == 3:  # 复数数据 (N, M, N)
                M, N = data_shape[1], data_shape[2]
                print(f"   --M {M} --N {N}")
            elif len(data_shape) == 4:  # 实数数据 (N, M, N, 2)
                M, N = data_shape[1], data_shape[2]
                print(f"   --M {M} --N {N}")
            
            # 推荐r值（通常是min(M,N)的一半或更小）
            r = min(M, N) // 2
            print(f"   --r {r}")
            
            # 计算输入维度
            input_dim = M * N * 2
            print(f"   输入维度: {input_dim}")
            
            # 推荐隐藏层维度
            hidden_dim = max(512, input_dim // 4)
            print(f"   推荐隐藏层维度: --hidden_dim {hidden_dim}")
    
    print("\n🚀 推荐的训练命令:")
    if data_shape and len(data_shape) >= 3:
        if len(data_shape) == 3:  # 复数数据
            M, N = data_shape[1], data_shape[2]
        else:  # 实数数据
            M, N = data_shape[1], data_shape[2]
        
        r = min(M, N) // 2
        hidden_dim = max(512, M * N * 2 // 4)
        
        print(f"python train_improved.py --M {M} --N {N} --r {r} --hidden_dim {hidden_dim} --epochs 100 --batch_size 8")

if __name__ == "__main__":
    main()
