#!/usr/bin/env python3
"""
Improved AI-Enabled Wireless Robust SVD Operator Training Script
改进的华为竞赛：AI驱动的无线鲁棒SVD算子训练脚本
"""

import argparse
import torch
import traceback
import os
from robust_svd_model import train_model

def main():
    parser = argparse.ArgumentParser(description='Train Improved Robust SVD Model')

    # 模型结构参数
    parser.add_argument('--M', type=int, default=64, help='Number of receive antennas')
    parser.add_argument('--N', type=int, default=64, help='Number of transmit antennas')
    parser.add_argument('--r', type=int, default=32, help='Rank of SVD decomposition')
    parser.add_argument('--hidden_dim', type=int, default=1024, help='Hidden layer dimension')

    # 训练参数
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')  # 减小batch_size以适应大矩阵
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')

    # 数据路径
    parser.add_argument('--data_path', type=str, 
                       default="D:/python/challenge/phase1/CompetitionData1/Round1TrainData1.npy", 
                       help='Path to input .npy file')
    parser.add_argument('--label_path', type=str, 
                       default="D:/python/challenge/phase1/CompetitionData1/Round1TrainLabel2.npy", 
                       help='Path to label .npy file')

    # 新增参数
    parser.add_argument('--use_cuda', action='store_true', help='Force use CUDA if available')
    parser.add_argument('--save_dir', type=str, default='.', help='Directory to save models')

    args = parser.parse_args()

    print("=" * 80)
    print("🚀 IMPROVED AI-Enabled Wireless Robust SVD Operator Training")
    print("=" * 80)
    print(f"📊 Model Parameters:")
    print(f"   M (receive antennas): {args.M}")
    print(f"   N (transmit antennas): {args.N}")
    print(f"   r (SVD rank): {args.r}")
    print(f"   Hidden dimension: {args.hidden_dim}")
    print(f"🎯 Training Parameters:")
    print(f"   Epochs: {args.epochs}")
    print(f"   Batch size: {args.batch_size}")
    print(f"   Learning rate: {args.lr}")
    print(f"📁 Data Paths:")
    print(f"   Input data: {args.data_path}")
    print(f"   Label data: {args.label_path}")
    print(f"💾 Save directory: {args.save_dir}")
    print("=" * 80)

    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ CUDA available. GPU: {torch.cuda.get_device_name()}")
        if args.use_cuda:
            print("🔥 Using GPU acceleration")
    else:
        print("⚠️ CUDA not available. Using CPU.")

    # 检查数据文件
    if not os.path.exists(args.data_path):
        print(f"❌ Data file not found: {args.data_path}")
        return 1
    if not os.path.exists(args.label_path):
        print(f"❌ Label file not found: {args.label_path}")
        return 1

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    try:
        print("\n🔄 Starting training...")
        model = train_model(
            M=args.M,
            N=args.N,
            r=args.r,
            hidden_dim=args.hidden_dim,
            num_epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.lr,
            data_path=args.data_path,
            label_path=args.label_path
        )

        print("\n" + "=" * 80)
        print("🎉 Training completed successfully!")
        print("📁 Model files saved:")
        print("   - best_robust_svd_model.pth (best validation loss)")
        print("   - final_robust_svd_model.pth (final epoch)")
        print("=" * 80)

        # 简单的模型验证
        print("\n🔍 Quick model validation...")
        model.eval()
        with torch.no_grad():
            # 创建一个测试输入
            test_input = torch.randn(1, args.M, args.N, 2)
            if torch.cuda.is_available() and args.use_cuda:
                test_input = test_input.cuda()
                model = model.cuda()
            
            U_test, S_test, V_test = model(test_input)
            print(f"✅ Model output shapes: U{U_test.shape}, S{S_test.shape}, V{V_test.shape}")
            print(f"✅ Singular values: {S_test[0].cpu().numpy()}")

    except Exception as e:
        print("❌ Training failed with error:")
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
