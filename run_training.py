#!/usr/bin/env python3
"""
AI-Enabled Wireless Robust SVD Operator Training Script
华为竞赛：AI驱动的无线鲁棒SVD算子训练脚本
"""

import argparse
import torch
import traceback
from robust_svd_model import train_model

def main():
    parser = argparse.ArgumentParser(description='Train Robust SVD Model')

    # 模型结构参数
    parser.add_argument('--M', type=int, default=8, help='Number of receive antennas')
    parser.add_argument('--N', type=int, default=8, help='Number of transmit antennas')
    parser.add_argument('--r', type=int, default=4, help='Rank of SVD decomposition')
    parser.add_argument('--hidden_dim', type=int, default=512, help='Hidden layer dimension')

    # 训练参数
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size')
    parser.add_argument('--lr', type=float, default=1e-3, help='Learning rate')

    # ✅ 加入默认的真实数据路径
    parser.add_argument('--data_path', type=str, default="D:/python/challenge/phase1/CompetitionData1/Round1TrainData1.npy", help='Path to input .npy file (e.g. data1)')
    parser.add_argument('--label_path', type=str, default="D:/python/challenge/phase1/CompetitionData1/Round1TrainLabel2.npy", help='Path to label .npy file (e.g. label2)')

    args = parser.parse_args()

    print("=" * 60)
    print("AI-Enabled Wireless Robust SVD Operator Training")
    print("=" * 60)
    print(f"Model Parameters:")
    print(f"  M (receive antennas): {args.M}")
    print(f"  N (transmit antennas): {args.N}")
    print(f"  r (SVD rank): {args.r}")
    print(f"  Hidden dimension: {args.hidden_dim}")
    print(f"Training Parameters:")
    print(f"  Epochs: {args.epochs}")
    print(f"  Batch size: {args.batch_size}")
    print(f"  Learning rate: {args.lr}")
    print(f"Data Paths:")
    print(f"  Input data: {args.data_path}")
    print(f"  Label data: {args.label_path}")
    print("=" * 60)

    if torch.cuda.is_available():
        print(f"✅ CUDA available. GPU: {torch.cuda.get_device_name()}")
    else:
        print("⚠️ CUDA not available. Using CPU.")

    try:
        model = train_model(
            M=args.M,
            N=args.N,
            r=args.r,
            hidden_dim=args.hidden_dim,
            num_epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.lr,
            data_path=args.data_path,
            label_path=args.label_path
        )

        print("\n" + "=" * 60)
        print("✅ Training completed successfully!")
        print("📁 Model files saved:")
        print("  - best_robust_svd_model.pth (best validation loss)")
        print("  - final_robust_svd_model.pth (final epoch)")
        print("=" * 60)

    except Exception as e:
        print("❌ Training failed with error:")
        traceback.print_exc()
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
