# 🚀 华为竞赛运行指南

## 📋 快速开始

### 方法一：一键运行完整流水线（推荐）

```bash
python run_complete_pipeline.py
```

这个脚本会自动完成：
1. ✅ 环境检查
2. 🚀 模型训练
3. 📦 生成提交文件
4. 🔍 验证提交包

### 方法二：分步运行

#### 1. 训练模型
```bash
# 使用真实数据训练（如果有）
python train_improved.py --epochs 100 --batch_size 16 --lr 0.001

# 或者使用原始训练脚本
python run_training.py --epochs 100 --batch_size 16 --lr 0.001
```

#### 2. 生成提交文件
```bash
# 评估并生成提交文件
python evaluate_improved.py --create_submission

# 或者使用原始推理脚本
python inference_and_submission.py
```

## 📁 输出文件说明

### 训练后生成的文件：
- `best_robust_svd_model.pth` - 最佳模型权重
- `final_robust_svd_model.pth` - 最终模型权重

### 提交文件：
- `submission/1.npz` - 第一个测试样本的结果
- `submission/2.npz` - 第二个测试样本的结果  
- `submission/3.npz` - 第三个测试样本的结果

### 最终提交包：
- `huawei_svd_submission_YYYYMMDD_HHMMSS.zip` - 符合比赛要求的提交包

## 📦 提交包内容

根据比赛要求，提交包包含：

1. **解决方案文件** (`{1-N}.npz`)
   - 每个文件包含：U, S, V, C
   - U: 左奇异向量矩阵
   - S: 奇异值向量
   - V: 右奇异向量矩阵
   - C: 计算复杂度（兆MAC）

2. **Python源代码** (`.py`)
   - `robust_svd_model.py` - 包含模型定义和训练代码

3. **训练权重** (`.pth`)
   - `best_robust_svd_model.pth` - 可用torch.nn.Module.load_state_dict()加载

## 🔧 环境要求

```bash
pip install torch numpy
# 可选：用于复杂度计算
pip install thop
```

## ⚙️ 参数配置

### 模型参数
- `M=8` - 接收天线数
- `N=8` - 发送天线数  
- `r=4` - SVD分解的秩
- `hidden_dim=512` - 隐藏层维度

### 训练参数
- `epochs=100` - 训练轮数
- `batch_size=16` - 批大小
- `lr=0.001` - 学习率

## 🎯 评分标准

比赛评分公式：**score = 100 × L_AE + C**

其中：
- `L_AE` = 近似误差 = `||H_label - UΣV^H||_F / ||H_label||_F + ||U^H U - I|| + ||V^H V - I||`
- `C` = 计算复杂度（兆MAC）

## 🔍 验证提交

运行完整流水线后，脚本会自动验证：
- ✅ 所有必需文件是否存在
- ✅ npz文件是否包含正确的键值
- ✅ 数据形状是否正确
- ✅ 模型文件是否可加载

## 📊 性能监控

训练过程中会显示：
- 训练损失和验证损失
- 学习率变化
- 早停信息
- 最佳模型保存信息

推理过程中会显示：
- 推理时间
- 计算复杂度
- 正交性误差
- 官方AE分数估算

## 🚨 常见问题

### 1. 数据路径问题
如果找不到真实数据文件，程序会自动使用合成数据进行训练。

### 2. CUDA问题
程序会自动检测CUDA可用性，如果没有GPU会使用CPU训练。

### 3. 内存不足
可以减少batch_size或hidden_dim：
```bash
python train_improved.py --batch_size 8 --hidden_dim 256
```

### 4. 训练时间过长
可以减少epochs进行快速测试：
```bash
python train_improved.py --epochs 20
```

## 📈 改进建议

1. **数据质量**：使用真实的竞赛数据进行训练
2. **超参数调优**：尝试不同的学习率、批大小等
3. **模型架构**：调整隐藏层维度、添加更多层
4. **训练策略**：增加训练轮数、使用数据增强

## 📞 技术支持

如果遇到问题：
1. 检查Python环境和依赖包
2. 确认数据路径正确
3. 查看错误日志信息
4. 尝试使用较小的参数进行测试

---

**注意**：确保最终提交的zip文件符合比赛要求，包含所有必需的文件和正确的格式。
