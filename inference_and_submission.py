import torch
import numpy as np
from robust_svd_model import RobustSVDNet, complex_matrix_to_tensor, tensor_to_complex_matrix
import time

def load_model(model_path, M, N, r):
    """加载训练好的模型"""
    model = RobustSVDNet(M, N, r)
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    model.eval()
    return model

def inference_single(model, H_nonideal):
    """对单个样本进行推理"""
    device = next(model.parameters()).device

    with torch.no_grad():
        # 确保输入是正确的形状 (1, M, N, 2)
        if len(H_nonideal.shape) == 3:
            H_nonideal = H_nonideal.unsqueeze(0)

        H_nonideal = H_nonideal.to(device)

        # 推理
        start_time = time.time()
        U_pred, S_pred, V_pred = model(H_nonideal)
        inference_time = time.time() - start_time

        # 转换为numpy
        U_pred = U_pred.cpu().numpy()[0]  # (M, r, 2)
        S_pred = S_pred.cpu().numpy()[0]  # (r,)
        V_pred = V_pred.cpu().numpy()[0]  # (N, r, 2)

        return U_pred, S_pred, V_pred, inference_time

def estimate_complexity(model, M, N, sample_input):
    """估算模型复杂度（以MAC为单位）"""
    try:
        from thop import profile
        macs, params = profile(model, inputs=(sample_input,))
        return macs / 1e6  # 转换为兆MAC
    except ImportError:
        print("Warning: thop not installed, using rough estimation")
        # 粗略估算
        total_params = sum(p.numel() for p in model.parameters())
        # 假设每个参数对应一个MAC操作
        return total_params / 1e6

def create_submission_files(model, test_data_list, output_dir="submission"):
    """创建提交文件"""
    import os
    os.makedirs(output_dir, exist_ok=True)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    for i, H_nonideal in enumerate(test_data_list, 1):
        # 推理
        U_pred, S_pred, V_pred, inference_time = inference_single(model, H_nonideal)

        # 估算复杂度
        sample_input = H_nonideal.unsqueeze(0).to(device) if len(H_nonideal.shape) == 3 else H_nonideal.to(device)
        complexity = estimate_complexity(model, H_nonideal.shape[0], H_nonideal.shape[1], sample_input)

        # 保存为npz文件
        output_file = os.path.join(output_dir, f"{i}.npz")
        np.savez(output_file,
                 U=U_pred,
                 S=S_pred,
                 V=V_pred,
                 C=complexity)

        print(f"Saved {output_file}, Complexity: {complexity:.2f} mega MACs, Time: {inference_time:.4f}s")

def load_test_data(data_path):
    """加载测试数据（根据实际数据格式调整）"""
    # 这里需要根据实际的测试数据格式进行调整
    # 假设测试数据是.npz格式
    try:
        data = np.load(data_path)
        # 假设数据键名为'H_nonideal'
        H_nonideal = data['H_nonideal']

        # 如果是复数数组，转换为实数张量格式
        if H_nonideal.dtype == np.complex64 or H_nonideal.dtype == np.complex128:
            H_tensor = torch.zeros((*H_nonideal.shape, 2))
            H_tensor[..., 0] = torch.from_numpy(H_nonideal.real)
            H_tensor[..., 1] = torch.from_numpy(H_nonideal.imag)
            return H_tensor
        else:
            return torch.from_numpy(H_nonideal)
    except:
        print(f"Error loading {data_path}, generating synthetic test data")
        return generate_test_data()

def generate_test_data(num_samples=3, M=8, N=8, noise_level=0.2):
    """生成测试数据（用于演示）"""
    test_data = []

    for _ in range(num_samples):
        # 生成随机复数矩阵
        H_real = torch.randn(M, N)
        H_imag = torch.randn(M, N)
        H_complex = torch.complex(H_real, H_imag)

        # 添加噪声
        noise = torch.randn_like(H_complex) * noise_level
        H_nonideal = H_complex + noise

        # 转换为张量格式
        H_tensor = complex_matrix_to_tensor(H_nonideal)
        test_data.append(H_tensor)

    return test_data

def evaluate_model(model, test_data_list):
    """评估模型性能"""
    total_time = 0
    total_complexity = 0

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    print("Evaluating model performance...")

    for i, H_nonideal in enumerate(test_data_list):
        U_pred, S_pred, V_pred, inference_time = inference_single(model, H_nonideal)

        # 估算复杂度
        sample_input = H_nonideal.unsqueeze(0).to(device) if len(H_nonideal.shape) == 3 else H_nonideal.to(device)
        complexity = estimate_complexity(model, H_nonideal.shape[0], H_nonideal.shape[1], sample_input)

        total_time += inference_time
        total_complexity += complexity

        print(f"Sample {i+1}: Time={inference_time:.4f}s, Complexity={complexity:.2f} mega MACs")

        # 验证输出形状
        print(f"  U shape: {U_pred.shape}, S shape: {S_pred.shape}, V shape: {V_pred.shape}")

        # 验证正交性
        U_complex = torch.complex(torch.from_numpy(U_pred[:, :, 0]), torch.from_numpy(U_pred[:, :, 1]))
        V_complex = torch.complex(torch.from_numpy(V_pred[:, :, 0]), torch.from_numpy(V_pred[:, :, 1]))

        orthogonal_error_U = torch.norm(U_complex.conj().T @ U_complex - torch.eye(U_complex.shape[1]), p='fro')
        orthogonal_error_V = torch.norm(V_complex.conj().T @ V_complex - torch.eye(V_complex.shape[1]), p='fro')

        print(f"  Orthogonal error - U: {orthogonal_error_U:.6f}, V: {orthogonal_error_V:.6f}")

    avg_time = total_time / len(test_data_list)
    avg_complexity = total_complexity / len(test_data_list)

    print(f"\nAverage inference time: {avg_time:.4f}s")
    print(f"Average complexity: {avg_complexity:.2f} mega MACs")

    return avg_time, avg_complexity

def main():
    """主函数"""
    # 模型参数（需要与训练时一致）
    M, N, r = 8, 8, 4

    # 加载模型
    try:
        model = load_model('best_robust_svd_model.pth', M, N, r)
        print("Loaded best model")
    except:
        try:
            model = load_model('final_robust_svd_model.pth', M, N, r)
            print("Loaded final model")
        except:
            print("No trained model found, please train the model first")
            return

    # 生成或加载测试数据
    test_data_list = generate_test_data(num_samples=3, M=M, N=N)
    print(f"Generated {len(test_data_list)} test samples")

    # 评估模型
    evaluate_model(model, test_data_list)

    # 创建提交文件
    create_submission_files(model, test_data_list)
    print("Submission files created successfully!")

if __name__ == "__main__":
    main()
