# 🔧 快速修复指南

## 🎯 问题解决

你遇到的错误是因为**模型参数与数据维度不匹配**。

### 📊 数据分析结果
- 你的数据形状: `(20000, 64, 64, 2)`
- 输入维度: `64 × 64 × 2 = 8192`
- 但模型默认设计为: `8 × 8 × 2 = 128`

## ✅ 解决方案

### 方法1: 使用正确的参数运行（推荐）

```bash
python train_improved.py --M 64 --N 64 --r 32 --hidden_dim 1024 --epochs 50 --batch_size 4
```

### 方法2: 使用原始训练脚本

```bash
python run_training.py --M 64 --N 64 --r 32 --hidden_dim 1024 --epochs 50 --batch_size 4
```

### 方法3: 如果内存不足，使用更小的参数

```bash
python train_improved.py --M 64 --N 64 --r 16 --hidden_dim 512 --epochs 50 --batch_size 2
```

## 🚀 推荐的完整训练命令

```bash
# 快速测试（20分钟左右）
python train_improved.py --M 64 --N 64 --r 32 --hidden_dim 1024 --epochs 20 --batch_size 4

# 完整训练（1-2小时）
python train_improved.py --M 64 --N 64 --r 32 --hidden_dim 1024 --epochs 100 --batch_size 4

# 如果GPU内存不足
python train_improved.py --M 64 --N 64 --r 16 --hidden_dim 512 --epochs 50 --batch_size 2
```

## 📋 参数说明

- `--M 64 --N 64`: 匹配你的数据维度（64×64矩阵）
- `--r 32`: SVD分解的秩（可以调整为16或24）
- `--hidden_dim 1024`: 隐藏层维度（可以调整为512或2048）
- `--epochs 50`: 训练轮数（可以先用20测试）
- `--batch_size 4`: 批大小（如果内存不足可以改为2）

## 🔍 验证修复

运行训练前，可以先验证参数：

```bash
python -c "
from robust_svd_model import RobustSVDNet
import torch

# 测试模型创建
model = RobustSVDNet(64, 64, 32, 1024)
print('✅ 模型创建成功')

# 测试前向传播
test_input = torch.randn(2, 64, 64, 2)
U, S, V = model(test_input)
print(f'✅ 前向传播成功: U{U.shape}, S{S.shape}, V{V.shape}')
"
```

## 💡 性能优化建议

### 如果训练太慢：
1. 减少batch_size: `--batch_size 2`
2. 减少hidden_dim: `--hidden_dim 512`
3. 减少r: `--r 16`

### 如果内存不足：
1. 使用更小的batch_size: `--batch_size 1`
2. 减少hidden_dim: `--hidden_dim 256`

### 如果想要更好的精度：
1. 增加epochs: `--epochs 200`
2. 增加hidden_dim: `--hidden_dim 2048`
3. 使用更小的学习率: `--lr 0.0005`

## 🎯 现在就试试

直接复制粘贴这个命令：

```bash
python train_improved.py --M 64 --N 64 --r 32 --hidden_dim 1024 --epochs 50 --batch_size 4
```

这应该能解决你的问题！训练完成后，你就可以生成提交文件了。
