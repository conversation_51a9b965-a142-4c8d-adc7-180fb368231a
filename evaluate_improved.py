#!/usr/bin/env python3
"""
Improved Evaluation and Submission Script
改进的评估和提交脚本
"""

import torch
import numpy as np
import argparse
import os
from robust_svd_model import RobustSVDNet, complex_matrix_to_tensor
from inference_and_submission import (
    load_model, inference_single, estimate_complexity, 
    evaluate_model, create_submission_files, generate_test_data,
    compute_official_ae_metric
)

def load_test_data_with_labels(data_path, label_path=None):
    """加载测试数据和标签（如果有）"""
    try:
        # 加载测试数据
        if data_path.endswith('.npy'):
            data = np.load(data_path)
            if data.dtype in [np.complex64, np.complex128]:
                # 复数数据
                H_tensor = torch.zeros((*data.shape, 2))
                H_tensor[..., 0] = torch.from_numpy(data.real)
                H_tensor[..., 1] = torch.from_numpy(data.imag)
            else:
                # 实数数据 (M, N, 2)
                H_tensor = torch.from_numpy(data)
        else:
            raise ValueError("Unsupported file format")
        
        # 加载标签（如果提供）
        labels = None
        if label_path and os.path.exists(label_path):
            label_data = np.load(label_path)
            if label_data.dtype in [np.complex64, np.complex128]:
                labels = torch.zeros((*label_data.shape, 2))
                labels[..., 0] = torch.from_numpy(label_data.real)
                labels[..., 1] = torch.from_numpy(label_data.imag)
            else:
                labels = torch.from_numpy(label_data)
        
        # 如果是单个样本，转换为列表
        if len(H_tensor.shape) == 3:  # (M, N, 2)
            test_data_list = [H_tensor]
            labels_list = [labels] if labels is not None else None
        else:  # (num_samples, M, N, 2)
            test_data_list = [H_tensor[i] for i in range(H_tensor.shape[0])]
            labels_list = [labels[i] for i in range(labels.shape[0])] if labels is not None else None
            
        return test_data_list, labels_list
        
    except Exception as e:
        print(f"❌ Error loading test data: {e}")
        print("🔄 Generating synthetic test data...")
        return generate_test_data(num_samples=3), None

def comprehensive_evaluation(model, test_data_list, labels_list=None):
    """全面的模型评估"""
    print("\n" + "=" * 80)
    print("🔬 COMPREHENSIVE MODEL EVALUATION")
    print("=" * 80)
    
    # 基本性能评估
    avg_time, avg_complexity = evaluate_model(model, test_data_list, labels_list)
    
    # 详细的SVD质量分析
    print("\n📊 DETAILED SVD QUALITY ANALYSIS")
    print("-" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    reconstruction_errors = []
    orthogonality_errors = []
    singular_value_properties = []
    
    for i, H_nonideal in enumerate(test_data_list):
        U_pred, S_pred, V_pred, _ = inference_single(model, H_nonideal)
        
        # 转换为复数进行分析
        U_complex = torch.complex(torch.from_numpy(U_pred[:, :, 0]), torch.from_numpy(U_pred[:, :, 1]))
        V_complex = torch.complex(torch.from_numpy(V_pred[:, :, 0]), torch.from_numpy(V_pred[:, :, 1]))
        S_tensor = torch.from_numpy(S_pred)
        
        # 重构误差分析
        H_reconstructed = U_complex @ torch.diag(S_tensor.to(torch.complex64)) @ V_complex.conj().T
        H_input_complex = torch.complex(H_nonideal[:, :, 0], H_nonideal[:, :, 1])
        
        recon_error = torch.norm(H_input_complex - H_reconstructed, p='fro') / torch.norm(H_input_complex, p='fro')
        reconstruction_errors.append(recon_error.item())
        
        # 正交性分析
        I_r = torch.eye(U_complex.shape[1], dtype=U_complex.dtype)
        ortho_U = torch.norm(U_complex.conj().T @ U_complex - I_r, p='fro').item()
        ortho_V = torch.norm(V_complex.conj().T @ V_complex - I_r, p='fro').item()
        orthogonality_errors.append((ortho_U, ortho_V))
        
        # 奇异值属性
        sv_properties = {
            'min': S_pred.min(),
            'max': S_pred.max(),
            'mean': S_pred.mean(),
            'std': S_pred.std(),
            'condition_number': S_pred.max() / (S_pred.min() + 1e-8),
            'is_sorted': all(S_pred[j] >= S_pred[j+1] for j in range(len(S_pred)-1))
        }
        singular_value_properties.append(sv_properties)
    
    # 统计总结
    print(f"📈 Reconstruction Error Statistics:")
    print(f"   Mean: {np.mean(reconstruction_errors):.6f}")
    print(f"   Std:  {np.std(reconstruction_errors):.6f}")
    print(f"   Max:  {np.max(reconstruction_errors):.6f}")
    
    print(f"⚖️  Orthogonality Error Statistics:")
    ortho_U_errors = [err[0] for err in orthogonality_errors]
    ortho_V_errors = [err[1] for err in orthogonality_errors]
    print(f"   U - Mean: {np.mean(ortho_U_errors):.6f}, Max: {np.max(ortho_U_errors):.6f}")
    print(f"   V - Mean: {np.mean(ortho_V_errors):.6f}, Max: {np.max(ortho_V_errors):.6f}")
    
    print(f"🔢 Singular Value Properties:")
    avg_condition = np.mean([sv['condition_number'] for sv in singular_value_properties])
    sorted_count = sum([sv['is_sorted'] for sv in singular_value_properties])
    print(f"   Average Condition Number: {avg_condition:.2f}")
    print(f"   Properly Sorted: {sorted_count}/{len(singular_value_properties)}")
    
    return {
        'avg_time': avg_time,
        'avg_complexity': avg_complexity,
        'reconstruction_errors': reconstruction_errors,
        'orthogonality_errors': orthogonality_errors,
        'singular_value_properties': singular_value_properties
    }

def main():
    parser = argparse.ArgumentParser(description='Evaluate Improved Robust SVD Model')
    
    # 模型参数
    parser.add_argument('--M', type=int, default=8, help='Number of receive antennas')
    parser.add_argument('--N', type=int, default=8, help='Number of transmit antennas')
    parser.add_argument('--r', type=int, default=4, help='Rank of SVD decomposition')
    
    # 文件路径
    parser.add_argument('--model_path', type=str, default='best_robust_svd_model.pth', 
                       help='Path to trained model')
    parser.add_argument('--test_data', type=str, default=None, 
                       help='Path to test data file')
    parser.add_argument('--test_labels', type=str, default=None, 
                       help='Path to test labels file (optional)')
    
    # 评估选项
    parser.add_argument('--create_submission', action='store_true', 
                       help='Create submission files')
    parser.add_argument('--comprehensive', action='store_true', 
                       help='Run comprehensive evaluation')
    
    args = parser.parse_args()
    
    print("🔍 IMPROVED MODEL EVALUATION")
    print("=" * 50)
    
    # 加载模型
    try:
        model = load_model(args.model_path, args.M, args.N, args.r)
        print(f"✅ Model loaded from: {args.model_path}")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return 1
    
    # 加载测试数据
    if args.test_data:
        test_data_list, labels_list = load_test_data_with_labels(args.test_data, args.test_labels)
        print(f"✅ Loaded {len(test_data_list)} test samples")
    else:
        test_data_list = generate_test_data(num_samples=3, M=args.M, N=args.N)
        labels_list = None
        print(f"✅ Generated {len(test_data_list)} synthetic test samples")
    
    # 运行评估
    if args.comprehensive:
        results = comprehensive_evaluation(model, test_data_list, labels_list)
    else:
        evaluate_model(model, test_data_list, labels_list)
    
    # 创建提交文件
    if args.create_submission:
        print("\n📦 Creating submission files...")
        create_submission_files(model, test_data_list)
        print("✅ Submission files created successfully!")
    
    print("\n🎉 Evaluation completed!")

if __name__ == "__main__":
    exit(main())
