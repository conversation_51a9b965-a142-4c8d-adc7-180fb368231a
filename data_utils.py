# data_utils.py

import numpy as np
import torch
from torch.utils.data import Dataset


def load_complex_npy(path):
    """
    从 .npy 文件加载复数矩阵（实部+虚部）合成为复数形式
    """
    arr = np.load(path)  # shape: (N, M, N, 2)
    return arr[..., 0] + 1j * arr[..., 1]


def complex_to_tensor(cmat):
    """
    将复数矩阵转换为实部+虚部两个通道，形状为 (..., 2)
    """
    return torch.tensor(np.stack([cmat.real, cmat.imag], axis=-1), dtype=torch.float32)


def load_real_dataset(data_path, label_path, r):
    """
    从 data.npy 和 label.npy 中加载训练数据，并对标签做 SVD 拆解
    返回 X（非理想输入）, Y_U, Y_S, Y_V（SVD标签）
    """
    print(f"🔄 正在加载数据：\n - 输入: {data_path}\n - 标签: {label_path}")
    H_data = load_complex_npy(data_path)
    H_label = load_complex_npy(label_path)

    assert H_data.shape[0] == H_label.shape[0], "❌ 样本数不一致，不能配对训练！"

    U_list, S_list, V_list = [], [], []
    for h in H_label:
        U, S, Vh = np.linalg.svd(h)
        U_list.append(U[:, :r])
        S_list.append(S[:r])
        V_list.append(Vh.conj().T[:, :r])

    # 转换为张量
    X = complex_to_tensor(H_data)                # shape: (N, M, N, 2)
    Y_U = complex_to_tensor(np.array(U_list))    # shape: (N, M, r, 2)
    Y_S = torch.tensor(np.array(S_list), dtype=torch.float32)  # shape: (N, r)
    Y_V = complex_to_tensor(np.array(V_list))    # shape: (N, N, r, 2)

    print("✅ 数据加载完成！")
    print(f"X:    {X.shape}")
    print(f"Y_U:  {Y_U.shape}")
    print(f"Y_S:  {Y_S.shape}")
    print(f"Y_V:  {Y_V.shape}")

    return X, Y_U, Y_S, Y_V


class RealSVDDataset(Dataset):
    """
    PyTorch Dataset，用于加载已处理的真实数据
    """
    def __init__(self, X, Y_U, Y_S, Y_V):
        self.X = X
        self.Y_U = Y_U
        self.Y_S = Y_S
        self.Y_V = Y_V

    def __len__(self):
        return self.X.shape[0]

    def __getitem__(self, idx):
        return self.X[idx], self.Y_U[idx], self.Y_S[idx], self.Y_V[idx]
