import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, random_split, Dataset
import torch.nn.functional as F
from data_utils import load_real_dataset, RealSVDDataset


class ComplexLinear(nn.Module):
    """处理复数的线性层"""
    def __init__(self, in_features, out_features):
        super(ComplexLinear, self).__init__()
        self.real_linear = nn.Linear(in_features, out_features)
        self.imag_linear = nn.Linear(in_features, out_features)

    def forward(self, x):
        # x shape: (batch, ..., 2) where last dim is [real, imag]
        real_part = x[..., 0]
        imag_part = x[..., 1]

        real_out = self.real_linear(real_part) - self.imag_linear(imag_part)
        imag_out = self.real_linear(imag_part) + self.imag_linear(real_part)

        return torch.stack([real_out, imag_out], dim=-1)

class RobustSVDNet(nn.Module):
    """鲁棒SVD神经网络"""
    def __init__(self, M, N, r, hidden_dim=512):
        super(RobustSVDNet, self).__init__()
        self.M = M  # 接收天线数
        self.N = N  # 发送天线数
        self.r = r  # SVD分解的秩
        self.hidden_dim = hidden_dim

        # 输入维度：M*N*2 (复数矩阵展平)
        input_dim = M * N * 2
        print(f"✅ Creating model with input_dim={input_dim}")

        # 编码器网络
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )

        # U矩阵预测分支 (M, r, 2)
        self.u_branch = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, M * r * 2)
        )

        # S矩阵预测分支 (r,)
        self.s_branch = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, r),

        )
        nn.init.kaiming_uniform_(self.s_branch[-1].weight, a=0.1)
        nn.init.zeros_(self.s_branch[-1].bias)

        # V矩阵预测分支 (N, r, 2)
        self.v_branch = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, N * r * 2)
        )

    def forward(self, H_nonideal):
        # H_nonideal shape: (batch, M, N, 2)
        batch_size = H_nonideal.shape[0]

        # 展平输入
        x = H_nonideal.view(batch_size, -1)

        # 编码
        features = self.encoder(x)

        # 预测U, S, V
        U_flat = self.u_branch(features)
        S = self.s_branch(features)
        V_flat = self.v_branch(features)

        # 重塑输出
        U = U_flat.view(batch_size, self.M, self.r, 2)
        V = V_flat.view(batch_size, self.N, self.r, 2)

        # 对U和V进行正交化
        U = self.orthogonalize_complex(U)
        V = self.orthogonalize_complex(V)

        return U, S, V

    def orthogonalize_complex(self, X):
        """对复数矩阵进行正交化"""
        batch_size = X.shape[0]
        result = torch.zeros_like(X)

        for b in range(batch_size):
            # 转换为复数
            X_complex = torch.complex(X[b, :, :, 0], X[b, :, :, 1])

            # QR分解进行正交化
            Q, _ = torch.linalg.qr(X_complex)

            # 转换回实部虚部表示
            result[b, :, :, 0] = Q.real
            result[b, :, :, 1] = Q.imag

        return result

class RealSVDDataset(Dataset):
    def __init__(self, H_nonideal, Y_U, Y_S, Y_V):
        self.H = H_nonideal
        self.Y_U = Y_U
        self.Y_S = Y_S
        self.Y_V = Y_V

    def __len__(self):
        return self.H.shape[0]

    def __getitem__(self, idx):
        return self.H[idx], self.Y_U[idx], self.Y_S[idx], self.Y_V[idx]

def complex_matrix_to_tensor(H_complex):
    """将复数矩阵转换为实数张量"""
    return torch.stack([H_complex.real, H_complex.imag], dim=-1)

def tensor_to_complex_matrix(H_tensor):
    """将实数张量转换为复数矩阵"""
    return torch.complex(H_tensor[..., 0], H_tensor[..., 1])

def compute_svd_loss(U_pred, S_pred, V_pred, U_true, S_true, V_true, alpha=1.0, beta=0.01):
    """
    alpha: 重构误差的权重
    beta: 奇异值误差的权重
    """
    batch_size = U_pred.shape[0]
    total_loss = 0.0

    for b in range(batch_size):
        U_p = torch.complex(U_pred[b, :, :, 0], U_pred[b, :, :, 1])
        S_p = S_pred[b]
        V_p = torch.complex(V_pred[b, :, :, 0], V_pred[b, :, :, 1])

        U_t = torch.complex(U_true[b, :, :, 0], U_true[b, :, :, 1])
        S_t = S_true[b]
        V_t = torch.complex(V_true[b, :, :, 0], V_true[b, :, :, 1])

        H_pred = U_p @ torch.diag(S_p.to(torch.complex64)) @ V_p.conj().T
        H_true = U_t @ torch.diag(S_t.to(torch.complex64)) @ V_t.conj().T

        # 计算重构误差（归一化）
        recon_error = torch.norm(H_pred - H_true, p='fro') / torch.norm(H_true, p='fro')

        # 正交性损失
        I_r = torch.eye(U_p.shape[1], device=U_p.device, dtype=U_p.dtype)
        ortho_u = torch.norm(U_p.conj().T @ U_p - I_r, p='fro')
        ortho_v = torch.norm(V_p.conj().T @ V_p - I_r, p='fro')

        # 奇异值监督损失（MSE）
        s_loss = F.mse_loss(S_p, S_t)

        total_loss += alpha * recon_error + ortho_u + ortho_v + beta * s_loss

        # 可选：调试输出
        if b == 0:
            print(f"[DEBUG] recon_error: {recon_error.item():.4f}, ortho_u: {ortho_u.item():.4f}, ortho_v: {ortho_v.item():.4f}, s_loss: {s_loss.item():.4f}")
            print(f"  S_pred: {S_p.detach().cpu().numpy()}")
            print(f"  S_true: {S_t.detach().cpu().numpy()}")

    return total_loss / batch_size



def generate_synthetic_data(num_samples, M, N, r, noise_level=0.1):
    """生成合成训练数据"""
    H_ideal_list = []
    H_nonideal_list = []

    for _ in range(num_samples):
        # 生成理想信道矩阵
        U_true = torch.randn(M, r, dtype=torch.complex64)
        U_true, _ = torch.linalg.qr(U_true)

        V_true = torch.randn(N, r, dtype=torch.complex64)
        V_true, _ = torch.linalg.qr(V_true)

        S_true = torch.rand(r, dtype=torch.float32) * 2 + 0.1  # 奇异值在[0.1, 2.1]范围内
        S_true = torch.sort(S_true, descending=True)[0]

        H_ideal = U_true @ torch.diag(S_true.to(torch.complex64)) @ V_true.conj().T

        # 添加噪声生成非理想信道
        noise = torch.randn_like(H_ideal) * noise_level
        H_nonideal = H_ideal + noise

        H_ideal_list.append(complex_matrix_to_tensor(H_ideal))
        H_nonideal_list.append(complex_matrix_to_tensor(H_nonideal))

    return torch.stack(H_nonideal_list), torch.stack(H_ideal_list)

def train_model(M=64, N=64, r=32, hidden_dim=512, num_epochs=100, batch_size=32, learning_rate=1e-3, data_path=None, label_path=None):
    """使用真实数据训练 SVD 网络模型"""

    import os
    from torch.utils.data import DataLoader, random_split
    from data_utils import load_real_dataset, RealSVDDataset  # 确保你已创建并导入这些

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"✅ Using device: {device}")

    # === Step 1: 加载你自己的数据集 ===
    if not (data_path and label_path):
        raise ValueError("data_path 和 label_path 必须提供")

    if not (os.path.exists(data_path) and os.path.exists(label_path)):
        raise FileNotFoundError("提供的数据或标签路径不存在")

    X, Y_U, Y_S, Y_V = load_real_dataset(data_path, label_path, r)

    # 划分训练/验证集
    dataset = RealSVDDataset(X, Y_U, Y_S, Y_V)
    train_size = int(0.9 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    # === Step 2: 构建模型 ===
    model = RobustSVDNet(M, N, r, hidden_dim=hidden_dim).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.5)

    # === Step 3: 训练循环 ===
    best_val_loss = float('inf')

    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0

        for H_nonideal, Y_U_batch, Y_S_batch, Y_V_batch in train_loader:
            H_nonideal = H_nonideal.to(device)
            Y_U_batch = Y_U_batch.to(device)
            Y_S_batch = Y_S_batch.to(device)
            Y_V_batch = Y_V_batch.to(device)

            optimizer.zero_grad()

            U_pred, S_pred, V_pred = model(H_nonideal)
            S_pred.retain_grad()
            loss = compute_svd_loss(U_pred, S_pred, V_pred, Y_U_batch, Y_S_batch, Y_V_batch)

            loss.backward()
            print("S_pred grad:", S_pred.grad)
            optimizer.step()
            train_loss += loss.item()

        # 验证阶段
        model.eval()
        val_loss = 0.0

        with torch.no_grad():
            for H_nonideal, Y_U_batch, Y_S_batch, Y_V_batch in val_loader:
                H_nonideal = H_nonideal.to(device)
                Y_U_batch = Y_U_batch.to(device)
                Y_S_batch = Y_S_batch.to(device)
                Y_V_batch = Y_V_batch.to(device)

                U_pred, S_pred, V_pred = model(H_nonideal)
                loss = compute_svd_loss(U_pred, S_pred, V_pred, Y_U_batch, Y_S_batch, Y_V_batch)
                val_loss += loss.item()

        train_loss /= len(train_loader)
        val_loss /= len(val_loader)

        print(f'Epoch [{epoch+1}/{num_epochs}] | Train Loss: {train_loss:.6f} | Val Loss: {val_loss:.6f}')

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), 'best_robust_svd_model.pth')

        scheduler.step()

    print("✅ Training completed!")
    torch.save(model.state_dict(), 'final_robust_svd_model.pth')
    print("📁 模型已保存为 final_robust_svd_model.pth")

    return model


if __name__ == "__main__":
    # 训练模型
    model = train_model(M=8, N=8, r=4, num_epochs=100)

    # 保存最终模型
    torch.save(model.state_dict(), 'final_robust_svd_model.pth')
    print("Model saved successfully!")
