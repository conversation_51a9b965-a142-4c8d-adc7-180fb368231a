# AI-Enabled Wireless Robust SVD Operator

华为竞赛：AI驱动的无线鲁棒SVD算子

## 项目概述

本项目实现了一个基于神经网络的鲁棒SVD算子，用于从非理想无线信道矩阵中恢复理想信道矩阵的SVD分解。

### 核心功能
- 从非理想信道矩阵预测理想信道的U、S、V矩阵
- 保证U和V矩阵的正交性
- 优化精度和计算复杂度的平衡

## 文件结构

```
├── robust_svd_model.py      # 主要的神经网络模型
├── inference_and_submission.py  # 推理和提交文件生成
├── run_training.py          # 训练脚本
├── requirements.txt         # 依赖包列表
└── README.md               # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 训练模型

使用默认参数训练：
```bash
python run_training.py
```

使用自定义参数训练：
```bash
python run_training.py --M 8 --N 8 --r 4 --epochs 100 --batch_size 32 --lr 0.001
```

参数说明：
- `--M`: 接收天线数量 (默认: 8)
- `--N`: 发送天线数量 (默认: 8)
- `--r`: SVD分解的秩 (默认: 4)
- `--epochs`: 训练轮数 (默认: 100)
- `--batch_size`: 批大小 (默认: 32)
- `--lr`: 学习率 (默认: 0.001)

### 2. 推理和生成提交文件

```bash
python inference_and_submission.py
```

这将：
- 加载训练好的模型
- 生成测试数据（或加载真实测试数据）
- 评估模型性能
- 生成提交所需的.npz文件

## 模型架构

### RobustSVDNet
- **输入**: 非理想信道矩阵 H_nonideal (M×N×2, 复数表示为实部虚部)
- **输出**:
  - U: 左奇异向量 (M×r×2)
  - S: 奇异值 (r,)
  - V: 右奇异向量 (N×r×2)

### 网络结构
1. **编码器**: 多层全连接网络，提取特征
2. **三个分支**: 分别预测U、S、V矩阵
3. **正交化**: 使用QR分解确保U和V的正交性

## 损失函数

损失函数包含三个部分：
1. **重构误差**: ||H_ideal - USV^H||_F
2. **U正交性约束**: ||U^H U - I||_F
3. **V正交性约束**: ||V^H V - I||_F

总损失 = (重构误差 + 正交性约束) / ||H_ideal||_F

## 评分标准

根据竞赛要求，最终得分为：
**score = 100 × L_AE + C**

其中：
- L_AE: 近似误差的加权平均
- C: 计算复杂度（兆MAC）

## 提交格式

生成的提交文件包含：
- `{1-3}.npz`: 包含U、S、V、C的数据文件
- `robust_svd_model.py`: 源代码文件
- `best_robust_svd_model.pth`: 训练好的模型参数

## 性能优化

1. **精度优化**:
   - 使用QR分解保证正交性
   - 多层网络提取复杂特征
   - 正交性损失约束

2. **效率优化**:
   - 合理的网络深度和宽度
   - Dropout防止过拟合
   - 学习率调度

## 注意事项

1. 确保输入数据格式正确：复数矩阵需转换为(M, N, 2)的实数张量
2. 模型参数M、N、r需要与训练时保持一致
3. 推理时会自动进行正交化处理
4. 支持GPU加速训练和推理

## 故障排除

1. **CUDA内存不足**: 减小batch_size
2. **训练不收敛**: 调整学习率或增加训练轮数
3. **精度不够**: 增加网络深度或隐藏层维度
4. **复杂度过高**: 减少网络参数或使用模型压缩技术
