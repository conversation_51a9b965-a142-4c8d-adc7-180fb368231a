import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, random_split, Dataset
import torch.nn.functional as F
from data_utils import load_real_dataset, RealSVDDataset


class ComplexLinear(nn.Module):
    """处理复数的线性层"""
    def __init__(self, in_features, out_features):
        super(ComplexLinear, self).__init__()
        self.real_linear = nn.Linear(in_features, out_features)
        self.imag_linear = nn.Linear(in_features, out_features)

    def forward(self, x):
        # x shape: (batch, ..., 2) where last dim is [real, imag]
        real_part = x[..., 0]
        imag_part = x[..., 1]

        real_out = self.real_linear(real_part) - self.imag_linear(imag_part)
        imag_out = self.real_linear(imag_part) + self.imag_linear(real_part)

        return torch.stack([real_out, imag_out], dim=-1)

class AttentionBlock(nn.Module):
    """简单的自注意力模块"""
    def __init__(self, dim):
        super(AttentionBlock, self).__init__()
        self.attention = nn.MultiheadAttention(dim, num_heads=8, batch_first=True)
        self.norm = nn.LayerNorm(dim)

    def forward(self, x):
        # x shape: (batch, seq_len, dim)
        attn_out, _ = self.attention(x, x, x)
        return self.norm(x + attn_out)

class RobustSVDNet(nn.Module):
    """改进的鲁棒SVD神经网络"""
    def __init__(self, M, N, r, hidden_dim=512):
        super(RobustSVDNet, self).__init__()
        self.M = M  # 接收天线数
        self.N = N  # 发送天线数
        self.r = r  # SVD分解的秩
        self.hidden_dim = hidden_dim

        # 输入维度：M*N*2 (复数矩阵展平)
        input_dim = M * N * 2
        print(f"✅ Creating improved model with input_dim={input_dim}")

        # 输入归一化
        self.input_norm = nn.LayerNorm(input_dim)

        # 改进的编码器网络（添加残差连接）
        self.encoder1 = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        self.encoder2 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        self.encoder3 = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # 注意力机制（可选）
        self.use_attention = hidden_dim <= 512  # 只在较小模型中使用
        if self.use_attention:
            self.attention = AttentionBlock(hidden_dim)

        # 改进的U矩阵预测分支
        self.u_branch = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(hidden_dim // 2, M * r * 2)
        )

        # 改进的S矩阵预测分支（确保奇异值为正且降序）
        self.s_branch = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(hidden_dim // 2, r)
        )
        # 特殊初始化确保奇异值预测稳定
        nn.init.xavier_uniform_(self.s_branch[-1].weight)
        nn.init.constant_(self.s_branch[-1].bias, 1.0)  # 初始化为正值

        # 改进的V矩阵预测分支
        self.v_branch = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(hidden_dim // 2, N * r * 2)
        )

    def forward(self, H_nonideal):
        # H_nonideal shape: (batch, M, N, 2)
        batch_size = H_nonideal.shape[0]

        # 展平输入并归一化
        x = H_nonideal.view(batch_size, -1)
        x = self.input_norm(x)

        # 多层编码（带残差连接）
        x1 = self.encoder1(x)
        x2 = self.encoder2(x1) + x1  # 残差连接
        features = self.encoder3(x2) + x2  # 残差连接

        # 可选的注意力机制
        if self.use_attention:
            # 重塑为序列格式用于注意力
            seq_features = features.unsqueeze(1)  # (batch, 1, hidden_dim)
            seq_features = self.attention(seq_features)
            features = seq_features.squeeze(1)

        # 预测U, S, V
        U_flat = self.u_branch(features)
        S_raw = self.s_branch(features)
        V_flat = self.v_branch(features)

        # 处理奇异值：确保为正且降序
        S = torch.abs(S_raw)  # 确保为正
        S = torch.sort(S, dim=1, descending=True)[0]  # 降序排列

        # 重塑输出
        U = U_flat.view(batch_size, self.M, self.r, 2)
        V = V_flat.view(batch_size, self.N, self.r, 2)

        # 对U和V进行正交化
        U = self.orthogonalize_complex(U)
        V = self.orthogonalize_complex(V)

        return U, S, V

    def orthogonalize_complex(self, X):
        """改进的复数矩阵正交化方法"""
        batch_size = X.shape[0]
        result = torch.zeros_like(X)

        for b in range(batch_size):
            # 转换为复数
            X_complex = torch.complex(X[b, :, :, 0], X[b, :, :, 1])

            try:
                # 使用QR分解进行正交化
                Q, R = torch.linalg.qr(X_complex)

                # 确保R的对角元素为正（标准化QR分解）
                diag_signs = torch.sign(torch.diag(R).real)
                diag_signs[diag_signs == 0] = 1  # 避免零值
                Q = Q * diag_signs.unsqueeze(0)

            except Exception as e:
                print(f"QR decomposition failed for batch {b}, using SVD fallback: {e}")
                # 备用方案：使用SVD
                U_svd, _, Vh_svd = torch.linalg.svd(X_complex)
                Q = U_svd @ Vh_svd  # 最接近的正交矩阵

            # 转换回实部虚部表示
            result[b, :, :, 0] = Q.real
            result[b, :, :, 1] = Q.imag

        return result

class RealSVDDataset(Dataset):
    def __init__(self, H_nonideal, Y_U, Y_S, Y_V):
        self.H = H_nonideal
        self.Y_U = Y_U
        self.Y_S = Y_S
        self.Y_V = Y_V

    def __len__(self):
        return self.H.shape[0]

    def __getitem__(self, idx):
        return self.H[idx], self.Y_U[idx], self.Y_S[idx], self.Y_V[idx]

def complex_matrix_to_tensor(H_complex):
    """将复数矩阵转换为实数张量"""
    return torch.stack([H_complex.real, H_complex.imag], dim=-1)

def tensor_to_complex_matrix(H_tensor):
    """将实数张量转换为复数矩阵"""
    return torch.complex(H_tensor[..., 0], H_tensor[..., 1])

def compute_official_ae_loss(U_pred, S_pred, V_pred, H_label_batch):
    """
    计算官方的Approximation Error (AE)损失函数
    L_AE = ||H_label - UΣV^H||_F / ||H_label||_F + ||U^H U - I|| + ||V^H V - I||

    Args:
        U_pred: 预测的U矩阵 (batch, M, r, 2)
        S_pred: 预测的奇异值 (batch, r)
        V_pred: 预测的V矩阵 (batch, N, r, 2)
        H_label_batch: 理想信道矩阵 (batch, M, N, 2)
    """
    batch_size = U_pred.shape[0]
    total_ae = 0.0

    for b in range(batch_size):
        # 转换为复数
        U_complex = torch.complex(U_pred[b, :, :, 0], U_pred[b, :, :, 1])
        S_complex = S_pred[b].to(torch.complex64)
        V_complex = torch.complex(V_pred[b, :, :, 0], V_pred[b, :, :, 1])
        H_label_complex = torch.complex(H_label_batch[b, :, :, 0], H_label_batch[b, :, :, 1])

        # 重构矩阵: H_reconstructed = U @ Σ @ V^H
        H_reconstructed = U_complex @ torch.diag(S_complex) @ V_complex.conj().T

        # 第一项：归一化重构误差
        reconstruction_error = torch.norm(H_label_complex - H_reconstructed, p='fro') / torch.norm(H_label_complex, p='fro')

        # 第二项：U的正交性误差 ||U^H U - I||
        I_r = torch.eye(U_complex.shape[1], device=U_complex.device, dtype=U_complex.dtype)
        ortho_error_U = torch.norm(U_complex.conj().T @ U_complex - I_r, p='fro')

        # 第三项：V的正交性误差 ||V^H V - I||
        ortho_error_V = torch.norm(V_complex.conj().T @ V_complex - I_r, p='fro')

        # 官方AE公式
        ae = reconstruction_error + ortho_error_U + ortho_error_V
        total_ae += ae

        # 调试输出（仅第一个样本）
        if b == 0:
            print(f"[AE DEBUG] recon_error: {reconstruction_error.item():.6f}, ortho_U: {ortho_error_U.item():.6f}, ortho_V: {ortho_error_V.item():.6f}")
            print(f"  Total AE: {ae.item():.6f}")
            print(f"  S_pred: {S_pred[b].detach().cpu().numpy()}")

    return total_ae / batch_size

def compute_svd_loss(U_pred, S_pred, V_pred, U_true, S_true, V_true, alpha=1.0, beta=0.01):
    """
    保留原有损失函数作为备用（用于监督学习）
    alpha: 重构误差的权重
    beta: 奇异值误差的权重
    """
    batch_size = U_pred.shape[0]
    total_loss = 0.0

    for b in range(batch_size):
        U_p = torch.complex(U_pred[b, :, :, 0], U_pred[b, :, :, 1])
        S_p = S_pred[b]
        V_p = torch.complex(V_pred[b, :, :, 0], V_pred[b, :, :, 1])

        U_t = torch.complex(U_true[b, :, :, 0], U_true[b, :, :, 1])
        S_t = S_true[b]
        V_t = torch.complex(V_true[b, :, :, 0], V_true[b, :, :, 1])

        H_pred = U_p @ torch.diag(S_p.to(torch.complex64)) @ V_p.conj().T
        H_true = U_t @ torch.diag(S_t.to(torch.complex64)) @ V_t.conj().T

        # 计算重构误差（归一化）
        recon_error = torch.norm(H_pred - H_true, p='fro') / torch.norm(H_true, p='fro')

        # 正交性损失
        I_r = torch.eye(U_p.shape[1], device=U_p.device, dtype=U_p.dtype)
        ortho_u = torch.norm(U_p.conj().T @ U_p - I_r, p='fro')
        ortho_v = torch.norm(V_p.conj().T @ V_p - I_r, p='fro')

        # 奇异值监督损失（MSE）
        s_loss = F.mse_loss(S_p, S_t)

        total_loss += alpha * recon_error + ortho_u + ortho_v + beta * s_loss

    return total_loss / batch_size



def generate_synthetic_data(num_samples, M, N, r, noise_level=0.1):
    """生成合成训练数据"""
    H_ideal_list = []
    H_nonideal_list = []

    for _ in range(num_samples):
        # 生成理想信道矩阵
        U_true = torch.randn(M, r, dtype=torch.complex64)
        U_true, _ = torch.linalg.qr(U_true)

        V_true = torch.randn(N, r, dtype=torch.complex64)
        V_true, _ = torch.linalg.qr(V_true)

        S_true = torch.rand(r, dtype=torch.float32) * 2 + 0.1  # 奇异值在[0.1, 2.1]范围内
        S_true = torch.sort(S_true, descending=True)[0]

        H_ideal = U_true @ torch.diag(S_true.to(torch.complex64)) @ V_true.conj().T

        # 添加噪声生成非理想信道
        noise = torch.randn_like(H_ideal) * noise_level
        H_nonideal = H_ideal + noise

        H_ideal_list.append(complex_matrix_to_tensor(H_ideal))
        H_nonideal_list.append(complex_matrix_to_tensor(H_nonideal))

    return torch.stack(H_nonideal_list), torch.stack(H_ideal_list)

def train_model(M=64, N=64, r=32, hidden_dim=512, num_epochs=100, batch_size=32, learning_rate=1e-3, data_path=None, label_path=None):
    """使用真实数据训练 SVD 网络模型"""

    import os
    from torch.utils.data import DataLoader, random_split
    from data_utils import load_real_dataset, RealSVDDataset, load_complex_npy, complex_to_tensor  # 确保你已创建并导入这些

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"✅ Using device: {device}")

    # === Step 1: 加载你自己的数据集 ===
    if not (data_path and label_path):
        raise ValueError("data_path 和 label_path 必须提供")

    if not (os.path.exists(data_path) and os.path.exists(label_path)):
        raise FileNotFoundError("提供的数据或标签路径不存在")

    X, Y_U, Y_S, Y_V = load_real_dataset(data_path, label_path, r)

    # 同时加载理想信道矩阵用于AE损失计算
    H_label = load_complex_npy(label_path)
    H_label_tensor = complex_to_tensor(H_label)

    # 创建包含理想信道的数据集
    class AEDataset(Dataset):
        def __init__(self, X, Y_U, Y_S, Y_V, H_label):
            self.X = X
            self.Y_U = Y_U
            self.Y_S = Y_S
            self.Y_V = Y_V
            self.H_label = H_label

        def __len__(self):
            return self.X.shape[0]

        def __getitem__(self, idx):
            return self.X[idx], self.Y_U[idx], self.Y_S[idx], self.Y_V[idx], self.H_label[idx]

    # 划分训练/验证集
    dataset = AEDataset(X, Y_U, Y_S, Y_V, H_label_tensor)
    train_size = int(0.9 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    # === Step 2: 构建模型 ===
    model = RobustSVDNet(M, N, r, hidden_dim=hidden_dim).to(device)

    # 改进的优化器配置
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=learning_rate,
        weight_decay=1e-4,  # L2正则化
        betas=(0.9, 0.999),
        eps=1e-8
    )

    # 改进的学习率调度
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,  # 初始重启周期
        T_mult=2,  # 周期倍增因子
        eta_min=learning_rate * 0.01  # 最小学习率
    )

    # 早停机制
    early_stopping_patience = 15
    early_stopping_counter = 0

    # === Step 3: 训练循环 ===
    best_val_loss = float('inf')

    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0

        for H_nonideal, Y_U_batch, Y_S_batch, Y_V_batch, H_label_batch in train_loader:
            H_nonideal = H_nonideal.to(device)
            Y_U_batch = Y_U_batch.to(device)
            Y_S_batch = Y_S_batch.to(device)
            Y_V_batch = Y_V_batch.to(device)
            H_label_batch = H_label_batch.to(device)

            optimizer.zero_grad()

            U_pred, S_pred, V_pred = model(H_nonideal)

            # 使用官方AE损失函数
            ae_loss = compute_official_ae_loss(U_pred, S_pred, V_pred, H_label_batch)

            # 可选：添加少量监督损失帮助训练稳定性
            supervised_loss = compute_svd_loss(U_pred, S_pred, V_pred, Y_U_batch, Y_S_batch, Y_V_batch)

            # 组合损失：主要使用AE损失，少量监督损失
            loss = ae_loss + 0.1 * supervised_loss

            loss.backward()
            # 添加梯度裁剪防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            train_loss += loss.item()

        # 验证阶段
        model.eval()
        val_loss = 0.0

        with torch.no_grad():
            for H_nonideal, Y_U_batch, Y_S_batch, Y_V_batch, H_label_batch in val_loader:
                H_nonideal = H_nonideal.to(device)
                Y_U_batch = Y_U_batch.to(device)
                Y_S_batch = Y_S_batch.to(device)
                Y_V_batch = Y_V_batch.to(device)
                H_label_batch = H_label_batch.to(device)

                U_pred, S_pred, V_pred = model(H_nonideal)

                # 验证时主要关注AE损失
                ae_loss = compute_official_ae_loss(U_pred, S_pred, V_pred, H_label_batch)
                val_loss += ae_loss.item()

        train_loss /= len(train_loader)
        val_loss /= len(val_loader)

        print(f'Epoch [{epoch+1}/{num_epochs}] | Train Loss: {train_loss:.6f} | Val Loss: {val_loss:.6f} | LR: {optimizer.param_groups[0]["lr"]:.2e}')

        # 保存最佳模型和早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            early_stopping_counter = 0
            torch.save(model.state_dict(), 'best_robust_svd_model.pth')
            print(f"✅ New best model saved! Val Loss: {val_loss:.6f}")
        else:
            early_stopping_counter += 1

        # 早停检查
        if early_stopping_counter >= early_stopping_patience:
            print(f"🛑 Early stopping triggered after {epoch+1} epochs")
            break

        scheduler.step()

    print("✅ Training completed!")
    torch.save(model.state_dict(), 'final_robust_svd_model.pth')
    print("📁 模型已保存为 final_robust_svd_model.pth")

    return model


if __name__ == "__main__":
    # 训练模型
    model = train_model(M=8, N=8, r=4, num_epochs=100)

    # 保存最终模型
    torch.save(model.state_dict(), 'final_robust_svd_model.pth')
    print("Model saved successfully!")
